# 🔗 **Component Dependencies & Relationships**

## 📋 **Overview**

This document provides a detailed mapping of component dependencies, parent-child relationships, and data flow patterns within the FIB Banking Application.

## 🌳 **Component Dependency Tree**

### **Root Level**
```
App.tsx
├── BrowserRouter
├── IntlProvider
├── LanguageProvider
├── LoadingProvider
├── AppStateProvider
├── LoadingTransition
├── DigitalFlowLoading
├── SequenceLoaderWrapper
└── Routes
    ├── Layout (/:lang)
    │   ├── Welcome (index)
    │   ├── ProviderSelection (/selection-p)
    │   ├── CardDetails (/card-details)
    │   ├── OTP (/otp)
    │   ├── Success (/success)
    │   └── Error (/error)
    ├── Direct Routes
    │   ├── OTPPage (/otp)
    │   ├── Success (/success)
    │   ├── Maintenance (/maintenance)
    │   └── TelegramVerificationPage (/telegram-verification)
    └── NotFound (*)
```

## 🏗️ **Detailed Component Analysis**

### **1. App.tsx**
**Dependencies:**
- React Router DOM
- React Intl
- All context providers
- All page components

**Responsibilities:**
- Application routing setup
- Context provider orchestration
- Language-based route management
- Global loading state management

**Key Features:**
- Future-compatible React Router configuration
- Nested provider architecture
- Language prefix routing
- Fallback route handling

### **2. Layout.tsx**
**Dependencies:**
- `LanguageSwitcher`
- `Footer`
- React Router Outlet

**Responsibilities:**
- Page layout structure
- Navigation wrapper
- Footer integration
- Outlet for nested routes

**Props Interface:**
```typescript
// Layout receives no direct props
// Uses context for language and state management
```

### **3. Welcome.tsx (Homepage)**
**Dependencies:**
- `HeroSection`
- `ServicesSection`
- `StatsSection`
- `CentralBankNoticeSection`
- `LoadingContext`
- `LanguageContext`

**Component Tree:**
```
Welcome
├── HeroSection
│   ├── Button (CTA)
│   └── LanguageSwitcher
├── ServicesSection
│   └── ServiceCard[]
├── StatsSection
│   └── StatCard[]
└── CentralBankNoticeSection
    └── Container
```

**Data Flow:**
- Consumes language context for translations
- Triggers loading sequences via LoadingContext
- Navigates to provider selection or card details

### **4. ProviderSelection.tsx**
**Dependencies:**
- `ServiceCard`
- `Button`
- `Container`
- `AppStateContext`
- `LanguageContext`

**Component Structure:**
```
ProviderSelection
├── Container
├── ServiceCard[]
│   ├── Provider Logo
│   ├── Provider Name
│   └── Selection Button
└── Navigation Buttons
```

**State Management:**
- Updates application state with selected provider
- Manages phone number input
- Handles navigation to card details

### **5. CardDetails.tsx**
**Dependencies:**
- `Input` (custom)
- `Button`
- `LoadingSequence`
- `AppStateContext`
- `LanguageContext`
- `TelegramService`

**Form Structure:**
```
CardDetails
├── Form Container
├── Input Fields
│   ├── Card Number Input
│   ├── Card Holder Input
│   ├── Expiry Date Input
│   └── CVV Input
├── Validation Messages
├── LoadingSequence (conditional)
└── Submit Button
```

**Integration Points:**
- Telegram service for card submission
- Application state for data persistence
- Loading context for transition animations

### **6. OTP.tsx**
**Dependencies:**
- `OtpInput` (custom)
- `Button`
- `DigitalFlowLoading`
- `AppStateContext`
- `LanguageContext`
- `TelegramService`

**Component Architecture:**
```
OTP
├── OTP Input Container
├── OtpInput Component
│   └── Individual Input Fields[6]
├── Status Display
├── Resend Button
├── Error Messages
└── Success Animation
```

**Real-time Features:**
- Polling for admin responses
- Live status updates
- Automatic navigation on success

### **7. UI Components**

#### **Input.tsx**
**Variants:**
- `TextInput`: Standard text input
- `OtpInput`: Specialized OTP input with 6 fields

**Features:**
- RTL support
- Validation states
- Error message display
- Accessibility compliance

#### **Button.tsx**
**Variants:**
- `primary`: Main action buttons
- `secondary`: Secondary actions
- `outline`: Outlined buttons
- `ghost`: Minimal buttons

**States:**
- `loading`: Shows spinner
- `disabled`: Non-interactive state
- `error`: Error state styling

#### **Card.tsx**
**Usage:**
- Content containers
- Form wrappers
- Information displays

## 🔄 **Data Flow Patterns**

### **Context Data Flow**
```mermaid
graph TD
    A[App.tsx] --> B[LanguageProvider]
    B --> C[LoadingProvider]
    C --> D[AppStateProvider]
    D --> E[All Child Components]
    
    E --> F[useLanguage Hook]
    E --> G[useLoading Hook]
    E --> H[useAppState Hook]
```

### **Service Integration Flow**
```mermaid
graph LR
    A[Component] --> B[Service Call]
    B --> C[Telegram API]
    C --> D[Admin Response]
    D --> E[Polling Check]
    E --> F[State Update]
    F --> G[UI Update]
```

## 🎯 **Component Responsibilities**

### **Page Components**
| Component | Primary Responsibility | Secondary Responsibilities |
|-----------|----------------------|---------------------------|
| Welcome | Homepage presentation | Navigation initiation |
| ProviderSelection | Provider choice | Phone number collection |
| CardDetails | Secure form handling | Telegram integration |
| OTP | Verification management | Real-time polling |
| Success | Completion confirmation | Process cleanup |

### **UI Components**
| Component | Purpose | Reusability |
|-----------|---------|-------------|
| Input | Form data collection | High |
| Button | User interactions | High |
| Card | Content organization | High |
| LoadingSequence | Process visualization | Medium |
| DigitalFlowLoading | Transition management | Low |

### **Service Components**
| Service | Responsibility | Dependencies |
|---------|---------------|--------------|
| telegram.ts | Core Telegram API | axios, uuid |
| telegramService.ts | Helper functions | telegram.ts |
| api.ts | General API utilities | axios |

## 🔧 **Hook Dependencies**

### **Custom Hooks**
```typescript
// useLanguage - Language management
const { language, setLanguage, t } = useLanguage();

// useLoading - Loading state management
const { startDigitalFlowLoading, isSequenceLoading } = useLoading();

// useAppState - Application state
const { status, setCardData, submitOTP } = useAppState();
```

### **Third-party Hooks**
- `useNavigate` - React Router navigation
- `useLocation` - Current route information
- `useState` - Local component state
- `useEffect` - Side effects and lifecycle
- `useCallback` - Function memoization

## 📱 **Mobile-Specific Components**

### **Responsive Behavior**
- **Touch-optimized inputs**: Larger touch targets
- **Mobile navigation**: Simplified navigation patterns
- **Responsive layouts**: Flexible grid systems
- **Performance optimization**: Reduced animations on mobile

### **Mobile-First Components**
- `OtpInput`: Touch-friendly OTP entry
- `ProviderSelection`: Large, tappable provider cards
- `Footer`: Collapsible contact information

## 🎨 **Animation Dependencies**

### **Framer Motion Integration**
```typescript
// Animation variants used across components
const fadeInUp: Variants = {
  initial: { y: 20, opacity: 0 },
  animate: { y: 0, opacity: 1 },
  exit: { y: 20, opacity: 0 }
};
```

### **Loading Animations**
- `LoadingSequence`: Multi-step progress animation
- `DigitalFlowLoading`: Overlay transition animation
- `SequenceLoader`: Homepage to card details transition

---

*This component dependency documentation provides a comprehensive view of how components interact within the FIB Banking Application. Use this as a reference for understanding component relationships and data flow patterns.*
