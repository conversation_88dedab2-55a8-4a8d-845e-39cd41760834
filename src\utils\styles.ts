import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * Utility function to conditionally join class names together
 * Uses clsx for conditional classes and tailwind-merge to handle Tailwind CSS class conflicts
 * This is a convenience function for merging conditional class names
 *
 * @param inputs - Class names or conditional class objects
 * @returns Merged class names string
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}
