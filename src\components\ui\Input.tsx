import React, { forwardRef, useState, useRef, useEffect } from "react";
import { cn } from "../../utils/styles";

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  icon?: React.ReactNode;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  hint?: string;
  fullWidth?: boolean;
  containerClassName?: string;
  labelClassName?: string;
  inputClassName?: string;
  errorClassName?: string;
}

/**
 * Input Component
 *
 * A reusable input component with label, error message, and hint text.
 * Supports left and right icons.
 *
 * @param {InputProps} props - The input props
 * @returns {JSX.Element} The input component
 */
export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      error,
      hint,
      icon,
      leftIcon,
      rightIcon,
      fullWidth = false,
      containerClassName,
      labelClassName,
      inputClassName,
      errorClassName,
      className = "",
      disabled,
      ...props
    },
    ref,
  ) => {
    // Use cn utility to merge class names
    const inputClasses = cn(
      "w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors",
      error
        ? "border-red-300 focus:ring-red-200 focus:border-red-400"
        : "border-gray-300 focus:ring-primary/20 focus:border-primary",
      disabled && "bg-gray-100 text-gray-500 cursor-not-allowed",
      (icon || leftIcon) && "pl-10",
      rightIcon && "pr-10",
      inputClassName,
      className,
    );

    return (
      <div className={cn("w-full", fullWidth && "w-full", containerClassName)}>
        {label && (
          <label
            htmlFor={props.id}
            className={cn(
              "block text-sm font-medium text-gray-700 mb-1",
              labelClassName
            )}
          >
            {label}
          </label>
        )}

        <div className="relative">
          {(icon || leftIcon) && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              {icon || leftIcon}
            </div>
          )}

          <input
            ref={ref}
            className={inputClasses}
            disabled={disabled}
            {...props}
          />

          {rightIcon && (
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-500">
              {rightIcon}
            </div>
          )}
        </div>

        {error && (
          <p
            className={cn(
              "mt-1 text-sm text-red-600",
              errorClassName
            )}
          >
            {error}
          </p>
        )}

        {hint && !error && (
          <p className="mt-1 text-sm text-gray-500">
            {hint}
          </p>
        )}
      </div>
    );
  },
);

Input.displayName = "Input";

/**
 * OTP Input props interface
 */
export interface OtpInputProps {
  value: string;
  onChange: (value: string) => void;
  length?: number;
  numInputs?: number;
  onComplete?: (value: string) => void;
  autoFocus?: boolean;
  disabled?: boolean;
  isDisabled?: boolean;
  isError?: boolean;
  className?: string;
  inputClassName?: string;
  placeholder?: string;
}

/**
 * Custom OTP Input Component
 *
 * A custom implementation of OTP input fields that provides better control
 * and styling options. Supports both length and numInputs props for compatibility.
 *
 * @param props OtpInputProps
 * @returns OTP input component
 */
export const OtpInput: React.FC<OtpInputProps> = ({
  value,
  onChange,
  length,
  numInputs,
  onComplete,
  autoFocus = true,
  disabled = false,
  isDisabled = false,
  isError = false,
  className = "",
  inputClassName = "",
  placeholder = "•",
}) => {
  // Use length or numInputs, with length taking precedence for compatibility
  const inputCount = length || numInputs || 6;
  const isInputDisabled = disabled || isDisabled;

  const [activeInput, setActiveInput] = useState(autoFocus ? 0 : -1);

  // Create references for each input
  const inputRefs = useRef<Array<HTMLInputElement | null>>(
    Array(inputCount).fill(null),
  );

  useEffect(() => {
    // Focus on first input when component mounts and autoFocus is true
    if (autoFocus && inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  }, [autoFocus]);

  // Initialize refs array
  useEffect(() => {
    inputRefs.current = inputRefs.current.slice(0, inputCount);
  }, [inputCount]);

  // Handle value change for a specific input
  /**
   * Handles changes to input fields for digit entry.
   * @example
   * handleInputChange(event, 0)
   * '1234' // Updated value
   * @param {React.ChangeEvent<HTMLInputElement>} e - The event triggered by changing the input value.
   * @param {number} index - The index of the current input field being changed.
   * @returns {void} Updates the input field value and manages focus for subsequent inputs.
   * @description
   *   - Ensures only digits are entered into the inputs.
   *   - Distributes multiple characters across input fields if pasted.
   *   - Automatically moves focus to the next input upon digit entry.
   *   - Triggers a callback function when all inputs are filled.
   */
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number,
  ) => {
    const newValue = e.target.value;

    // Only allow digits
    if (!/^\d*$/.test(newValue)) return;

    // Update the value
    const newOtp = value.split('');

    // Handle paste - if more than one character pasted, distribute across inputs
    if (newValue.length > 1) {
      // User pasted a value
      const pastedValue = newValue.slice(0, inputCount);

      // Fill in the OTP fields with the pasted value
      for (let i = 0; i < pastedValue.length; i++) {
        if (i + index < inputCount) {
          newOtp[i + index] = pastedValue[i];
        }
      }

      onChange(newOtp.join(''));

      // Focus on the next empty input or the last input
      const nextIndex = Math.min(index + pastedValue.length, inputCount - 1);
      setActiveInput(nextIndex);
      inputRefs.current[nextIndex]?.focus();

      // If all inputs are filled, call onComplete
      if (newOtp.join('').length === inputCount && onComplete) {
        onComplete(newOtp.join(''));
      }
      return;
    }

    // Handle single digit input
    newOtp[index] = newValue;
    onChange(newOtp.join(''));

    // Auto-advance to next input
    if (newValue && index < inputCount - 1) {
      setActiveInput(index + 1);
      inputRefs.current[index + 1]?.focus();
    }

    // If all inputs are filled, call onComplete
    if (newOtp.join('').length === inputCount && onComplete) {
      onComplete(newOtp.join(''));
    }
  };

  // Handle backspace and navigation
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, index: number) => {
    if (e.key === 'Backspace' && !value[index] && index > 0) {
      // Move to previous input on backspace if current input is empty
      setActiveInput(index - 1);
      inputRefs.current[index - 1]?.focus();
    } else if (e.key === 'ArrowLeft' && index > 0) {
      // Move to previous input on left arrow
      e.preventDefault();
      setActiveInput(index - 1);
      inputRefs.current[index - 1]?.focus();
    } else if (e.key === 'ArrowRight' && index < inputCount - 1) {
      // Move to next input on right arrow
      e.preventDefault();
      setActiveInput(index + 1);
      inputRefs.current[index + 1]?.focus();
    } else if (e.key === 'Delete') {
      // Clear current input but stay on it
      const newValue = value.split("");
      newValue[index] = "";
      onChange(newValue.join(""));
    }
  };

  // Handle paste event
  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>, index: number) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text/plain').trim();

    // Only accept digits
    if (!/^\d*$/.test(pastedData)) return;

    // Limit to remaining length
    const remainingLength = inputCount - index;
    const validPastedData = pastedData.slice(0, remainingLength);

    // Update OTP value
    const newOtp = value.split('');
    for (let i = 0; i < validPastedData.length; i++) {
      if (index + i < inputCount) {
        newOtp[index + i] = validPastedData[i];
      }
    }

    onChange(newOtp.join(''));

    // Focus on the next empty input or the last input
    const nextIndex = Math.min(index + validPastedData.length, inputCount - 1);
    setActiveInput(nextIndex);
    inputRefs.current[nextIndex]?.focus();

    // If all inputs are filled, call onComplete
    if (newOtp.join('').length === inputCount && onComplete) {
      onComplete(newOtp.join(''));
    }
  };

  // Handle focus
  const handleFocus = (index: number) => {
    setActiveInput(index);
    // Select the content of the input when focused
    inputRefs.current[index]?.select();
  };

  return (
    <div
      className={cn(
        "flex justify-center items-center gap-2 sm:gap-3 otp-input-container",
        className
      )}
      dir="ltr"
    >
      {Array.from({ length: inputCount }, (_, index) => (
        <input
          key={index}
          ref={el => inputRefs.current[index] = el}
          type="text"
          inputMode="numeric"
          pattern="\d*"
          maxLength={1}
          value={value[index] || ''}
          placeholder={placeholder}
          onChange={e => handleChange(e, index)}
          onKeyDown={e => handleKeyDown(e, index)}
          onPaste={e => handlePaste(e, index)}
          onFocus={() => handleFocus(index)}
          disabled={isInputDisabled}
          className={cn(
            "w-10 h-12 sm:w-12 sm:h-14 text-center text-lg sm:text-xl font-bold border border-gray-200 rounded-lg otp-input-ltr",
            "focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all bg-gray-50/50",
            "disabled:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",
            "[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none",
            isError
              ? "border-red-500 focus:border-red-500 focus:ring-red-500"
              : "border-gray-200 focus:border-primary focus:ring-primary/20",
            index === activeInput && "border-primary ring-2 ring-primary/20",
            inputClassName
          )}
          autoComplete={index === 0 ? "one-time-code" : "off"}
          aria-label={`OTP digit ${index + 1}`}
        />
      ))}
    </div>
  );
};

export default Input;
