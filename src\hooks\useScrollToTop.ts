import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * Custom hook to scroll to top of page on route change
 * This ensures that when users navigate to a new page, 
 * they start at the top of the page instead of maintaining 
 * the previous scroll position
 */
export const useScrollToTop = () => {
  const location = useLocation();

  useEffect(() => {
    // Scroll to top when location changes
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'smooth'
    });
  }, [location.pathname, location.search]);
};

/**
 * Alternative hook for instant scroll to top (no smooth animation)
 * Use this when you need immediate scroll behavior
 */
export const useInstantScrollToTop = () => {
  const location = useLocation();

  useEffect(() => {
    // Instant scroll to top when location changes
    window.scrollTo(0, 0);
  }, [location.pathname, location.search]);
};

/**
 * Hook to scroll to top only on specific route changes
 * @param routes - Array of route patterns to watch for
 */
export const useConditionalScrollToTop = (routes: string[]) => {
  const location = useLocation();

  useEffect(() => {
    const shouldScroll = routes.some(route => 
      location.pathname.includes(route)
    );

    if (shouldScroll) {
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: 'smooth'
      });
    }
  }, [location.pathname, location.search, routes]);
};
