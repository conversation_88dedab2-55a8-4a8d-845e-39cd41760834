/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: "#003630",
          light: "#004d45",
          dark: "#002b27",
        },
        secondary: {
          DEFAULT: "#134E4A",
          light: "#1a635e",
          dark: "#0d3935",
        },
        accent: {
          DEFAULT: "#F0FDF4",
          light: "#f5fef8",
          dark: "#e6fbeb",
        },
        light: "#FFFFFF",
        dark: "#121212",
        gray: {
          DEFAULT: "#333333",
          light: "#666666",
          dark: "#1A1A1A",
          100: "#f7f7f7",
          200: "#e6e6e6",
          300: "#d4d4d4",
        },
        success: {
          DEFAULT: "#2ecc71",
          light: "#40d47e",
          dark: "#27ae60",
        },
        warning: {
          DEFAULT: "#f1c40f",
          light: "#f4d03f",
          dark: "#c29d0b",
        },
        error: {
          DEFAULT: "#e74c3c",
          light: "#eb6b5e",
          dark: "#c0392b",
        },
      },
      fontFamily: {
        sfpro: ['"SF Pro Display"', "sans-serif"],
        abarlow: ["AbarLow", "sans-serif"],
      },
      animationDelay: {
        100: "100ms",
        200: "200ms",
        300: "300ms",
        400: "400ms",
        500: "500ms",
        600: "600ms",
        700: "700ms",
        800: "800ms",
        900: "900ms",
        1000: "1000ms",
      },
      fontSize: {
        hero: ["4rem", { lineHeight: "1.1" }],
        "hero-mobile": ["2.75rem", { lineHeight: "1.2" }],
        display: ["3rem", { lineHeight: "1.2" }],
        h1: ["2.5rem", { lineHeight: "1.25" }],
        h2: ["2rem", { lineHeight: "1.3" }],
        h3: ["1.75rem", { lineHeight: "1.35" }],
      },
      boxShadow: {
        header: "0 8px 24px rgba(0, 0, 0, 0.08)",
        card: "0 4px 12px rgba(0, 0, 0, 0.05)",
        "card-hover": "0 12px 24px rgba(0, 0, 0, 0.1)",
        glass: "0 8px 32px rgba(0, 0, 0, 0.08)",
        "glass-hover": "0 12px 40px rgba(0, 0, 0, 0.12)",
        button: "0 4px 16px rgba(0, 54, 48, 0.2)",
      },
      backdropBlur: {
        glass: "12px",
      },
      transitionDuration: {
        250: "250ms",
        400: "400ms",
        600: "600ms",
      },
      borderRadius: {
        30: "30px",
        20: "20px",
        "4xl": "2rem",
      },
      minWidth: {
        xs: "360px",
      },
      maxWidth: {
        "screen-max": "2560px",
        container: "1300px",
      },
      padding: {
        18: "18px",
      },
      zIndex: {
        12: "12",
        30: "30",
        100: "100",
      },
      animation: {
        float: "float 6s ease-in-out infinite",
        "pulse-slow": "pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite",
        "loading-dot": "loading 1.5s infinite",
      },
      keyframes: {
        float: {
          "0%, 100%": { transform: "translateY(0)" },
          "50%": { transform: "translateY(-10px)" },
        },
        loading: {
          "0%": { opacity: 0, transform: "scale(0.5)" },
          "50%": { opacity: 1, transform: "scale(1)" },
          "100%": { opacity: 0, transform: "scale(0.5)" },
        },
      },
    },
  },
  plugins: [
    function ({ addUtilities, theme }) {
      const animationDelays = theme("animationDelay", {});
      const utilities = Object.entries(animationDelays).map(([key, value]) => ({
        [`.animation-delay-${key}`]: { animationDelay: value },
      }));
      addUtilities(utilities);
    },
  ],
};
